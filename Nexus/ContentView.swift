import SwiftUI

struct ContentView: View {
    @Environment(\.searchViewModel) var searchViewModel: SearchViewModel
    @Environment(\.moduleManager) var moduleManager: ModuleManager
    @Environment(\.configurationManager) var configurationManager: ConfigurationManager
    @Environment(\.accessibilityManager) var accessibilityManager: AccessibilityManager
    @Environment(\.keyboardNavigationManager) var keyboardNavigationManager: KeyboardNavigationManager
    @State private var searchText = ""
    @FocusState private var isSearchFieldFocused: Bool

    var body: some View {
        VStack(spacing: 0) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .accessibilityHidden(true) // Decorative icon, screen reader should focus on text field

                TextField("Search Nexus...", text: $searchText)
                    .textFieldStyle(.plain)
                    .font(.system(size: accessibilityManager.adjustedFontSize(16)))
                    .focused($isSearchFieldFocused)
                    .accessibilityLabel("Search")
                    .accessibilityHint("Type to search notes, calculate expressions, or find applications")
                    .accessibilityValue(searchText.isEmpty ? "Empty" : searchText)
                    .onSubmit {
                        Task {
                            await searchViewModel.executeSelectedResult()
                        }
                    }
                    .onChange(of: searchText) { _, newValue in
                        searchViewModel.searchText = newValue
                        searchViewModel.triggerSearch()
                    }
                    .keyboardNavigable(BasicFocusableElement(
                        tabOrder: 1,
                        accessibilityLabel: "Search field",
                        onActivate: { isSearchFieldFocused = true }
                    ))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                isSearchFieldFocused ?
                                    accessibilityManager.contrastAdjustedColor(
                                        .accentColor,
                                        highContrastAlternative: .primary
                                    ) : Color.clear,
                                lineWidth: accessibilityManager.isHighContrastEnabled ? 2 : 1
                            )
                            .animation(
                                accessibilityManager.isReducedMotionEnabled ?
                                    .none :
                                    .easeInOut(duration: 0.2),
                                value: isSearchFieldFocused
                            )
                    )
            )
            .accessibilityElement(children: .contain)

            Divider()

            // Content area
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    if searchText.isEmpty {
                        // Welcome message
                        VStack(spacing: 16) {
                            Image(systemName: "command.circle")
                                .font(.system(size: accessibilityManager.adjustedFontSize(48)))
                                .foregroundColor(
                                    accessibilityManager.contrastAdjustedColor(
                                        .accentColor,
                                        highContrastAlternative: .primary
                                    )
                                )
                                .accessibilityLabel("Nexus application icon")

                            Text("Welcome to Nexus")
                                .font(.system(size: accessibilityManager.adjustedFontSize(20), weight: .semibold))
                                .accessibilityAddTraits(.isHeader)

                            Text("Start typing to search notes, calculate, or launch apps")
                                .font(.system(size: accessibilityManager.adjustedFontSize(16)))
                                .foregroundColor(
                                    accessibilityManager.contrastAdjustedColor(
                                        .secondary,
                                        highContrastAlternative: .primary.opacity(0.8)
                                    )
                                )
                                .multilineTextAlignment(.center)
                                .accessibilityLabel("Instructions: Start typing to search notes, calculate expressions, or launch applications")

                            // Show enabled modules
                            if !moduleManager.enabledModules.isEmpty {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Available Modules:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    ForEach(moduleManager.enabledModules, id: \.identifier) { module in
                                        HStack {
                                            Image(systemName: module.iconName)
                                                .foregroundColor(.accentColor)
                                            Text(module.displayName)
                                                .font(.caption)
                                        }
                                    }
                                }
                                .padding(.top, 16)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.top, 40)
                    } else if searchViewModel.isSearching {
                        // Loading state
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Searching...")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    } else if searchViewModel.searchResults.isEmpty {
                        // No results
                        VStack(spacing: 8) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: accessibilityManager.adjustedFontSize(32)))
                                .foregroundColor(
                                    accessibilityManager.contrastAdjustedColor(
                                        .secondary,
                                        highContrastAlternative: .primary.opacity(0.6)
                                    )
                                )
                                .accessibilityHidden(true) // Decorative icon
                            Text("No results found")
                                .font(.system(size: accessibilityManager.adjustedFontSize(16)))
                                .foregroundColor(
                                    accessibilityManager.contrastAdjustedColor(
                                        .secondary,
                                        highContrastAlternative: .primary.opacity(0.8)
                                    )
                                )
                                .accessibilityLabel("No search results found for \(searchText)")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.top, 40)
                        .accessibilityElement(children: .combine)
                        .transition(.opacity.combined(with: .scale(scale: 0.9)))
                        .animation(
                            accessibilityManager.isReducedMotionEnabled ?
                                .none :
                                .easeInOut(duration: 0.4),
                            value: searchViewModel.searchResults.isEmpty
                        )
                    } else {
                        // Search results
                        LazyVStack(alignment: .leading, spacing: 4) {
                            ForEach(Array(searchViewModel.searchResults.enumerated()), id: \.element.id) { index, result in
                                SearchResultRow(
                                    result: result,
                                    isSelected: index == searchViewModel.selectedResultIndex,
                                    resultIndex: index,
                                    totalResults: searchViewModel.searchResults.count
                                )
                                .onTapGesture {
                                    searchViewModel.selectedResultIndex = index
                                    Task {
                                        await searchViewModel.executeSelectedResult()
                                    }
                                }
                                .keyboardNavigable(SearchResultFocusableElement(
                                    tabOrder: index + 10, // Start after search field
                                    result: result,
                                    onActivate: {
                                        searchViewModel.selectedResultIndex = index
                                        Task {
                                            await searchViewModel.executeSelectedResult()
                                        }
                                    }
                                ))
                                .transition(.asymmetric(
                                    insertion: .opacity.combined(with: .move(edge: .top)),
                                    removal: .opacity.combined(with: .move(edge: .bottom))
                                ))
                                .animation(
                                    accessibilityManager.isReducedMotionEnabled ?
                                        .none :
                                        .easeInOut(duration: 0.3).delay(Double(index) * 0.05),
                                    value: searchViewModel.searchResults.count
                                )
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .accessibilityLabel("Search results")
                        .accessibilityHint("Use arrow keys to navigate, press Enter to select")
                    }
                }
                .padding()
            }
        }
        .frame(width: 400, height: 300)
        .reducedMotionSupport(
            value: searchViewModel.searchResults.count,
            normalAnimation: .easeInOut(duration: 0.3),
            reducedAnimation: .linear(duration: 0.1)
        )
        .onAppear {
            // Focus search field when view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isSearchFieldFocused = true
                keyboardNavigationManager.activateKeyboardNavigation()
            }
        }
        .onKeyPress(.tab) {
            keyboardNavigationManager.focusNext()
            return .handled
        }
        .onKeyPress(.upArrow) {
            searchViewModel.selectPrevious()
            return .handled
        }
        .onKeyPress(.downArrow) {
            searchViewModel.selectNext()
            return .handled
        }
    }
}

struct SearchResultRow: View {
    let result: SearchResult
    let isSelected: Bool
    let resultIndex: Int
    let totalResults: Int
    @Environment(\.accessibilityManager) private var accessibilityManager

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: result.iconName)
                .foregroundColor(.accentColor)
                .frame(width: 16, height: 16)
                .accessibilityHidden(true) // Icon is decorative, title provides context

            VStack(alignment: .leading, spacing: 2) {
                Text(result.title)
                    .font(.body)
                    .lineLimit(1)

                if let subtitle = result.subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }

            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(isSelected ?
                    accessibilityManager.contrastAdjustedColor(
                        Color.accentColor.opacity(0.2),
                        highContrastAlternative: Color.accentColor.opacity(0.4)
                    ) : Color.clear
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(isSelected ?
                    accessibilityManager.contrastAdjustedColor(
                        Color.accentColor,
                        highContrastAlternative: Color.primary
                    ) : Color.clear,
                    lineWidth: accessibilityManager.isHighContrastEnabled ? 2 : 1
                )
        )
        .reducedMotionSupport(
            value: isSelected,
            normalAnimation: .easeInOut(duration: 0.2),
            reducedAnimation: .linear(duration: 0.05)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
        .accessibilityHint("Double tap to execute")
        .accessibilityAddTraits(isSelected ? [.isButton, .isSelected] : [.isButton])
    }

    private var accessibilityDescription: String {
        let position = "Result \(resultIndex + 1) of \(totalResults)"
        let title = result.title
        let subtitle = result.subtitle ?? ""
        let selection = isSelected ? "Selected" : ""

        return [position, title, subtitle, selection]
            .filter { !$0.isEmpty }
            .joined(separator: ", ")
    }
}

/// Focusable element for search results
struct SearchResultFocusableElement: FocusableElement {
    let id = UUID()
    let tabOrder: Int
    let result: SearchResult
    let onActivateAction: () -> Void

    var accessibilityLabel: String {
        return "\(result.title) - \(result.subtitle ?? "")"
    }

    init(tabOrder: Int, result: SearchResult, onActivate: @escaping () -> Void) {
        self.tabOrder = tabOrder
        self.result = result
        self.onActivateAction = onActivate
    }

    func onActivate() {
        onActivateAction()
    }
}

#Preview {
    let moduleManager = ModuleManager()
    let searchViewModel = SearchViewModel(moduleManager: moduleManager)
    let configurationManager = ConfigurationManager()

    return ContentView()
        .environment(\.searchViewModel, searchViewModel)
        .environment(\.moduleManager, moduleManager)
        .environment(\.configurationManager, configurationManager)
}
