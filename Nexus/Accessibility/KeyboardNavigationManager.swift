import SwiftUI
import AppKit

/// Manager for enhanced keyboard navigation
@Observable
class KeyboardNavigationManager {
    /// Singleton instance
    static let shared = KeyboardNavigationManager()
    
    /// Current focus state
    var currentFocus: (any FocusableElement)?

    /// Available focusable elements
    private var focusableElements: [any FocusableElement] = []
    
    /// Whether keyboard navigation is active
    var isKeyboardNavigationActive: Bool = false
    
    private init() {
        setupKeyboardMonitoring()
    }
    
    /// Register a focusable element
    func register(_ element: any FocusableElement) {
        focusableElements.append(element)
        focusableElements.sort { $0.tabOrder < $1.tabOrder }
    }

    /// Unregister a focusable element
    func unregister(_ element: any FocusableElement) {
        focusableElements.removeAll { $0.id == element.id }
    }
    
    /// Move focus to next element
    func focusNext() {
        guard let current = currentFocus,
              let currentIndex = focusableElements.firstIndex(where: { $0.id == current.id }) else {
            focusFirst()
            return
        }
        
        let nextIndex = (currentIndex + 1) % focusableElements.count
        focus(focusableElements[nextIndex])
    }
    
    /// Move focus to previous element
    func focusPrevious() {
        guard let current = currentFocus,
              let currentIndex = focusableElements.firstIndex(where: { $0.id == current.id }) else {
            focusLast()
            return
        }
        
        let previousIndex = currentIndex == 0 ? focusableElements.count - 1 : currentIndex - 1
        focus(focusableElements[previousIndex])
    }
    
    /// Focus first element
    func focusFirst() {
        guard let first = focusableElements.first else { return }
        focus(first)
    }
    
    /// Focus last element
    func focusLast() {
        guard let last = focusableElements.last else { return }
        focus(last)
    }
    
    /// Focus specific element
    func focus(_ element: any FocusableElement) {
        currentFocus = element
        element.onFocus()
        
        // Announce focus change to VoiceOver
        AccessibilityManager.shared.announce(
            "Focused on \(element.accessibilityLabel)",
            priority: .low
        )
    }
    
    /// Handle keyboard input
    func handleKeyPress(_ event: NSEvent) -> Bool {
        guard isKeyboardNavigationActive else { return false }
        
        switch event.keyCode {
        case 48: // Tab
            if event.modifierFlags.contains(.shift) {
                focusPrevious()
            } else {
                focusNext()
            }
            return true
            
        case 36: // Enter/Return
            currentFocus?.onActivate()
            return true
            
        case 53: // Escape
            currentFocus?.onEscape()
            return true
            
        case 125: // Down arrow
            if currentFocus?.supportsArrowNavigation == true {
                currentFocus?.onArrowDown()
                return true
            }
            
        case 126: // Up arrow
            if currentFocus?.supportsArrowNavigation == true {
                currentFocus?.onArrowUp()
                return true
            }
            
        case 123: // Left arrow
            if currentFocus?.supportsArrowNavigation == true {
                currentFocus?.onArrowLeft()
                return true
            }
            
        case 124: // Right arrow
            if currentFocus?.supportsArrowNavigation == true {
                currentFocus?.onArrowRight()
                return true
            }
            
        default:
            break
        }
        
        return false
    }
    
    /// Setup keyboard event monitoring
    private func setupKeyboardMonitoring() {
        NSEvent.addLocalMonitorForEvents(matching: .keyDown) { [weak self] event in
            if self?.handleKeyPress(event) == true {
                return nil // Consume the event
            }
            return event
        }
    }
    
    /// Activate keyboard navigation mode
    func activateKeyboardNavigation() {
        isKeyboardNavigationActive = true
        focusFirst()
        
        AccessibilityManager.shared.announce(
            "Keyboard navigation activated",
            priority: .medium
        )
    }
    
    /// Deactivate keyboard navigation mode
    func deactivateKeyboardNavigation() {
        isKeyboardNavigationActive = false
        currentFocus = nil
        
        AccessibilityManager.shared.announce(
            "Keyboard navigation deactivated",
            priority: .low
        )
    }
}

/// Protocol for focusable elements
protocol FocusableElement: Identifiable {
    var id: UUID { get }
    var tabOrder: Int { get }
    var accessibilityLabel: String { get }
    var supportsArrowNavigation: Bool { get }
    
    func onFocus()
    func onActivate()
    func onEscape()
    func onArrowUp()
    func onArrowDown()
    func onArrowLeft()
    func onArrowRight()
}

/// Default implementations for FocusableElement
extension FocusableElement {
    var supportsArrowNavigation: Bool { false }
    
    func onFocus() {}
    func onActivate() {}
    func onEscape() {}
    func onArrowUp() {}
    func onArrowDown() {}
    func onArrowLeft() {}
    func onArrowRight() {}
}

/// SwiftUI Environment key for KeyboardNavigationManager
struct KeyboardNavigationManagerKey: EnvironmentKey {
    static let defaultValue = KeyboardNavigationManager.shared
}

extension EnvironmentValues {
    var keyboardNavigationManager: KeyboardNavigationManager {
        get { self[KeyboardNavigationManagerKey.self] }
        set { self[KeyboardNavigationManagerKey.self] = newValue }
    }
}

/// View modifier for keyboard navigation support
struct KeyboardNavigable: ViewModifier {
    @Environment(\.keyboardNavigationManager) private var navigationManager
    
    let element: any FocusableElement
    @State private var isFocused = false
    
    func body(content: Content) -> some View {
        content
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(Color.accentColor, lineWidth: isFocused ? 2 : 0)
                    .animation(.easeInOut(duration: 0.2), value: isFocused)
            )
            .onAppear {
                navigationManager.register(element)
            }
            .onDisappear {
                navigationManager.unregister(element)
            }
            .onChange(of: navigationManager.currentFocus?.id) { _, newFocusId in
                isFocused = newFocusId == element.id
            }
    }
}

extension View {
    /// Make view keyboard navigable
    func keyboardNavigable(_ element: any FocusableElement) -> some View {
        modifier(KeyboardNavigable(element: element))
    }
}

/// Basic implementation of FocusableElement for simple views
struct BasicFocusableElement: FocusableElement {
    let id = UUID()
    let tabOrder: Int
    let accessibilityLabel: String
    let onActivateAction: () -> Void
    let onEscapeAction: (() -> Void)?
    
    init(
        tabOrder: Int,
        accessibilityLabel: String,
        onActivate: @escaping () -> Void,
        onEscape: (() -> Void)? = nil
    ) {
        self.tabOrder = tabOrder
        self.accessibilityLabel = accessibilityLabel
        self.onActivateAction = onActivate
        self.onEscapeAction = onEscape
    }
    
    func onActivate() {
        onActivateAction()
    }
    
    func onEscape() {
        onEscapeAction?()
    }
}
