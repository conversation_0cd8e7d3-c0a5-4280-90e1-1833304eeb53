import SwiftUI
import AppKit

/// Manager for accessibility features and preferences
@Observable
class AccessibilityManager {
    /// Singleton instance
    static let shared = AccessibilityManager()
    
    /// Whether high contrast mode is enabled
    var isHighContrastEnabled: Bool = false
    
    /// Whether reduced motion is enabled
    var isReducedMotionEnabled: Bool = false
    
    /// Whether VoiceOver is running
    var isVoiceOverEnabled: Bool = false
    
    /// Whether keyboard navigation is preferred
    var prefersKeyboardNavigation: Bool = false
    
    /// Current accessibility font size multiplier
    var fontSizeMultiplier: Double = 1.0
    
    private init() {
        updateAccessibilitySettings()
        setupNotificationObservers()
    }
    
    /// Update accessibility settings from system preferences
    private func updateAccessibilitySettings() {
        // Check for high contrast
        isHighContrastEnabled = NSWorkspace.shared.accessibilityDisplayShouldIncreaseContrast
        
        // Check for reduced motion
        isReducedMotionEnabled = NSWorkspace.shared.accessibilityDisplayShouldReduceMotion
        
        // Check for VoiceOver
        isVoiceOverEnabled = NSWorkspace.shared.isVoiceOverEnabled
        
        // Check for keyboard navigation preference
        prefersKeyboardNavigation = NSApp.isFullKeyboardAccessEnabled
        
        // Get font size preference
        let userDefaults = UserDefaults.standard
        fontSizeMultiplier = userDefaults.double(forKey: "AccessibilityFontSizeMultiplier")
        if fontSizeMultiplier == 0 {
            fontSizeMultiplier = 1.0
        }
    }
    
    /// Setup notification observers for accessibility changes
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: NSWorkspace.accessibilityDisplayOptionsDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.updateAccessibilitySettings()
        }
        
        NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.updateAccessibilitySettings()
        }
    }
    
    /// Get appropriate animation duration based on reduced motion preference
    func animationDuration(_ defaultDuration: Double) -> Double {
        return isReducedMotionEnabled ? 0.0 : defaultDuration
    }
    
    /// Get appropriate font size based on accessibility preferences
    func adjustedFontSize(_ baseSize: CGFloat) -> CGFloat {
        return baseSize * fontSizeMultiplier
    }
    
    /// Get high contrast colors if enabled
    func contrastAdjustedColor(_ color: Color, highContrastAlternative: Color? = nil) -> Color {
        if isHighContrastEnabled, let alternative = highContrastAlternative {
            return alternative
        }
        return color
    }
    
    /// Announce text to VoiceOver
    func announce(_ text: String, priority: NSAccessibilityPriorityLevel = .medium) {
        guard isVoiceOverEnabled else { return }
        
        DispatchQueue.main.async {
            NSAccessibility.post(
                element: NSApp.mainWindow ?? NSApp,
                notification: .announcementRequested,
                userInfo: [
                    .announcement: text,
                    .priority: priority.rawValue
                ]
            )
        }
    }
    
    /// Post accessibility notification
    func postNotification(_ notification: NSAccessibilityNotificationName, for element: Any? = nil) {
        DispatchQueue.main.async {
            NSAccessibility.post(
                element: element ?? NSApp.mainWindow ?? NSApp,
                notification: notification
            )
        }
    }
}

/// SwiftUI Environment key for AccessibilityManager
struct AccessibilityManagerKey: EnvironmentKey {
    static let defaultValue = AccessibilityManager.shared
}

extension EnvironmentValues {
    var accessibilityManager: AccessibilityManager {
        get { self[AccessibilityManagerKey.self] }
        set { self[AccessibilityManagerKey.self] = newValue }
    }
}

/// View modifier for enhanced accessibility
struct AccessibilityEnhanced: ViewModifier {
    @Environment(\.accessibilityManager) private var accessibilityManager
    
    let label: String?
    let hint: String?
    let value: String?
    let traits: AccessibilityTraits
    let isButton: Bool
    let isHeader: Bool
    
    init(
        label: String? = nil,
        hint: String? = nil,
        value: String? = nil,
        traits: AccessibilityTraits = [],
        isButton: Bool = false,
        isHeader: Bool = false
    ) {
        self.label = label
        self.hint = hint
        self.value = value
        self.traits = traits
        self.isButton = isButton
        self.isHeader = isHeader
    }
    
    func body(content: Content) -> some View {
        content
            .accessibilityLabel(label ?? "")
            .accessibilityHint(hint ?? "")
            .accessibilityValue(value ?? "")
            .accessibilityAddTraits(combinedTraits)
            .font(.system(size: accessibilityManager.adjustedFontSize(16)))
    }
    
    private var combinedTraits: AccessibilityTraits {
        var allTraits = traits
        if isButton {
            allTraits.insert(.isButton)
        }
        if isHeader {
            allTraits.insert(.isHeader)
        }
        return allTraits
    }
}

extension View {
    /// Apply enhanced accessibility features
    func accessibilityEnhanced(
        label: String? = nil,
        hint: String? = nil,
        value: String? = nil,
        traits: AccessibilityTraits = [],
        isButton: Bool = false,
        isHeader: Bool = false
    ) -> some View {
        modifier(AccessibilityEnhanced(
            label: label,
            hint: hint,
            value: value,
            traits: traits,
            isButton: isButton,
            isHeader: isHeader
        ))
    }
    
    /// Apply high contrast support
    func highContrastSupport(
        normalColor: Color,
        highContrastColor: Color
    ) -> some View {
        modifier(HighContrastSupport(
            normalColor: normalColor,
            highContrastColor: highContrastColor
        ))
    }
    
    /// Apply reduced motion support
    func reducedMotionSupport<T: Equatable>(
        value: T,
        normalAnimation: Animation,
        reducedAnimation: Animation = .none
    ) -> some View {
        modifier(ReducedMotionSupport(
            value: value,
            normalAnimation: normalAnimation,
            reducedAnimation: reducedAnimation
        ))
    }
}

/// High contrast support modifier
struct HighContrastSupport: ViewModifier {
    @Environment(\.accessibilityManager) private var accessibilityManager
    
    let normalColor: Color
    let highContrastColor: Color
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(
                accessibilityManager.contrastAdjustedColor(
                    normalColor,
                    highContrastAlternative: highContrastColor
                )
            )
    }
}

/// Reduced motion support modifier
struct ReducedMotionSupport<T: Equatable>: ViewModifier {
    @Environment(\.accessibilityManager) private var accessibilityManager
    
    let value: T
    let normalAnimation: Animation
    let reducedAnimation: Animation
    
    func body(content: Content) -> some View {
        content
            .animation(
                accessibilityManager.isReducedMotionEnabled ? reducedAnimation : normalAnimation,
                value: value
            )
    }
}
