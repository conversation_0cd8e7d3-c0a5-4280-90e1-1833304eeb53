{"builtTestProducts": [{"binaryPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.xctest/Contents/MacOS/NexusPackageTests", "packagePath": "/Volumes/ssd/projects/new nexus", "productName": "NexusPackageTests"}], "copyCommands": {"/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/1c-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/3024.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/agate.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/an-old-hope.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/androidstudio.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apathy.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apprentice.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arduino-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arta.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ascetic.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ashes.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atlas.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark-reasonable.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bespin.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-bathory.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-burzum.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-dark-funeral.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-gorgoroth.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-immortal.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-khold.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-marduk.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-mayhem.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-nile.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-venom.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brewer.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bright.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brogrammer.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brown-paper.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/chalk.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/circus.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codepen-embed.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codeschool.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/color-brewer.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/colors.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupcake.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupertino.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-cherry.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-dimmer.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-icecap.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-saturated.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/danqing.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darcula.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark-violet.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darkmoss.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darktooth.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/decaf.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/devibeans.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dirtysea.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/docco.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dracula.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eighties.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/embers.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/espresso.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva-dim.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/far.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/felipec.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/flat.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/foundation.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/framer.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/fruit-soda.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gigavolt.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark-dimmed.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-gist.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gml.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/googlecode.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/green-screen.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-hard.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-medium.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-pale.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-soft.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-hard.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-medium.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-soft.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hardcore.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/helios.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/highlighter/highlight.min.js"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hopscotch.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hybrid.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/icy-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/idea.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/intellij-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ir-black.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isotope.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimber.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lightfair.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lioshi.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/london-tube.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/macintosh.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/magula.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/marrakesh.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/materia.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-darker.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-lighter.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-palenight.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-vivid.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mellow-purple.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mexico-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mocha.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mono-blue.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai-sublime.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nebula.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/night-owl.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nord.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nova.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/obsidian.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ocean.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/oceanicnext.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/one-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/onedark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/outrun-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pasque.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/phd.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pico.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pojoaque.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pop.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/porple.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/purebasic.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qualia.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/railscasts.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rainbow.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rebecca.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-dawn.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-moon.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-dawn.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-moon.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/routeros.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sagelight.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sandcastle.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/school-book.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/seti-ui.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shades-of-purple.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shapeshifter.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/snazzy.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/spacemacs.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/srcery.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summercamp.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sunburst.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tango.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tender.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-blue.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-bright.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-eighties.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/twilight.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs2015.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vulcan.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt-light.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/woodland.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dark.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dusk.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xt256.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/zenburn.min.css"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Info.plist"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ar.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/cs.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/de.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/en.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/es.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/fr.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/hu.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ja.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ko.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/nl.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/pt-BR.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ru.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/sk.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-Hans.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings"}]}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings": {"inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-TW.lproj/Localizable.strings"}], "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.Highlightr-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<Highlightr-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources"}], "isLibrary": true, "moduleName": "Highlightr", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Highlightr-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build", "wholeModuleOptimization": false}, "C.KeyboardShortcuts-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"}], "isLibrary": true, "moduleName": "KeyboardShortcuts", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-package-name", "keyboardshortcuts"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build", "wholeModuleOptimization": false}, "C.Markdown-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/CAtomic.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/buffer.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/chunk.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm-extension_api.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm_version.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark_ctype.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/export.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/footnotes.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/houdini.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/html.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/inlines.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/iterator.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/map.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/mutex.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/node.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/parser.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/plugin.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/references.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/registry.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/render.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/scanners.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/syntax_extension.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/utf8.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/autolink.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/ext_scanners.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/cmark-gfm-core-extensions.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/strikethrough.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/table.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/tagfilter.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/tasklist.h"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources"}], "isLibrary": true, "moduleName": "<PERSON><PERSON>", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markdown-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build", "wholeModuleOptimization": false}, "C.Nexus-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources"}], "isLibrary": false, "moduleName": "Nexus", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/AccessibilityManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/KeyboardNavigationManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/ConfigurationManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/EnvironmentKeys.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/ModuleManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/NexusModule.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/SearchViewModel.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/DataService.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/DatabaseManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/EditorView.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/NexusTextView.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/AppConfiguration.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/ClipboardManagerModule.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/NotesHubModule.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/QuickLauncherModule.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/SmartCalculatorModule.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/NexusApp.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/EncryptionManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/KeychainManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/SecurityManager.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/NotesWindow.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/SettingsView.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/SettingsWindowController.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "Nexus_main", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift", "/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift", "/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build", "wholeModuleOptimization": false}, "C.NexusPackageTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources"}], "isLibrary": true, "moduleName": "NexusPackageTests", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusPackageTests.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/NexusPackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusPackageTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build", "wholeModuleOptimization": false}, "C.NexusTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources"}], "isLibrary": true, "moduleName": "NexusTests", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/ConfigurationManagerTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/DataServiceTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/DatabaseManagerTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/EncryptionManagerTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/ModuleManagerTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/SearchViewModelTests.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/SecurityManagerTests.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build", "wholeModuleOptimization": false}, "C.SQLite-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources", "importPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources"}], "isLibrary": true, "moduleName": "SQLite", "moduleOutputPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule", "objects": ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Backup.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Blob.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Errors.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Result.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Statement.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Value.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Cipher.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS4.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS5.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/RTree.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Foundation.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Helpers.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaReader.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Coding.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Collation.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Expression.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Operators.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query+with.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Schema.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Setter.swift.o", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLite-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-package-name", "sqlite_swift"], "outputFileMapPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift"], "tempsPath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"Highlightr": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "Highlightr", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Highlightr-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "KeyboardShortcuts": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "KeyboardShortcuts", "-package-name", "keyboardshortcuts", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-package-name", "keyboardshortcuts", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Markdown": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "<PERSON><PERSON>", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markdown-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Nexus": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "Nexus", "-package-name", "new_nexus", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift", "/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift", "/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift", "/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift", "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "Nexus_main", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "NexusPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "NexusPackageTests", "-package-name", "new_nexus", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/NexusPackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "NexusTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "NexusTests", "-package-name", "new_nexus", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift", "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include", "-Xcc", "-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap", "-Xcc", "-I", "-Xcc", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "new_nexus", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "SQLite": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "SQLite", "-package-name", "sqlite_swift", "-incremental", "-c", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift", "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift", "-I", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLite-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-package-name", "sqlite_swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"CAtomic": [], "Highlightr": [], "KeyboardShortcuts": [], "Markdown": ["cmark_gfm_extensions", "cmark_gfm", "CAtomic"], "Nexus": ["SQLite", "KeyboardShortcuts", "Highlightr", "<PERSON><PERSON>", "cmark_gfm_extensions", "cmark_gfm", "CAtomic"], "NexusPackageTests": ["NexusTests", "Nexus", "SQLite", "KeyboardShortcuts", "Highlightr", "<PERSON><PERSON>", "cmark_gfm_extensions", "cmark_gfm", "CAtomic"], "NexusTests": ["Nexus", "SQLite", "KeyboardShortcuts", "Highlightr", "<PERSON><PERSON>", "cmark_gfm_extensions", "cmark_gfm", "CAtomic"], "SQLite": [], "SwiftToolchainCSQLite": [], "api_test": ["cmark_gfm_extensions", "cmark_gfm"], "cmark_gfm": [], "cmark_gfm_bin": ["cmark_gfm_extensions", "cmark_gfm"], "cmark_gfm_extensions": ["cmark_gfm"], "sqlite": ["SwiftToolchainCSQLite"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift"}, {"kind": "file", "name": "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources"}, "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}