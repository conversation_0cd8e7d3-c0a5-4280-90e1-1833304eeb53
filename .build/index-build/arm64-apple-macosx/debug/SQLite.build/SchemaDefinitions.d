/Volumes/ssd/projects/new\ nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions~partial.swiftmodule : /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/XPC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/unistd.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/sys_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Combine.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Dispatch.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_math.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_signal.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/System.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Foundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Observation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_stdio.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_errno.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/IOKit.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
