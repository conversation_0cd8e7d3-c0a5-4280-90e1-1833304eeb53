'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift'
