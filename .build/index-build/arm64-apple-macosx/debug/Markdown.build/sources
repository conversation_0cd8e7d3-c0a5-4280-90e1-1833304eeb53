'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift'
'/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift'
