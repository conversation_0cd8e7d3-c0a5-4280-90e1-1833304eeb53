/Volumes/ssd/projects/new\ nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup~partial.swiftmodule : /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/InlineHTML.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/TableHead.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive\ Nodes/Aside.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/InlineCode.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/Image.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/Table.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/CustomInline.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/Doxygen\ Commands/DoxygenNote.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/BlockQuote.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/BlockDirective.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Leaf\ Blocks/Heading.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/Strong.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/Strikethrough.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Inline\ Container\ Blocks/Paragraph.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Leaf\ Blocks/ThematicBreak.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/LineBreak.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/SoftBreak.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Leaf\ Blocks/HTMLBlock.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Leaf\ Blocks/CodeBlock.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/CustomBlock.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/Link.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/SymbolLink.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/TableCell.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/ListItem.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/Doxygen\ Commands/DoxygenDiscussion.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/InlineMarkup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/BlockMarkup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/InlineContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/BasicInlineContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/BlockContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/BasicBlockContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/TableCellContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural\ Restrictions/ListItemContainer.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/Doxygen\ Commands/DoxygenParameter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/InlineAttributes.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Containers/Emphasis.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/Doxygen\ Commands/DoxygenReturns.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/OrderedList.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Block\ Container\ Blocks/UnorderedList.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline\ Nodes/Inline\ Leaves/Text.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/TableRow.swift /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block\ Nodes/Tables/TableBody.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/XPC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/unistd.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/sys_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Combine.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Dispatch.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_math.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_signal.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/System.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Foundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Observation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_stdio.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_errno.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/IOKit.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/utf8.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/CAtomic.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/node.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark_ctype.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/houdini.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm-extension_api.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/chunk.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/html.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/plugin.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/syntax_extension.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm_version.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/map.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/render.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/buffer.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/parser.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/iterator.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/references.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/inlines.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/footnotes.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/extensions/include/cmark-gfm-core-extensions.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/scanners.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/export.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/registry.h /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap /Volumes/ssd/projects/new\ nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
