/*!
  Theme: Atelier Savanna
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#87928a;background:#171c19}.hljs ::selection,.hljs::selection{background-color:#526057;color:#87928a}.hljs-comment{color:#5f6d64}.hljs-tag{color:#78877d}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#87928a}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#b16139}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#9f713c}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#a07e3b}.hljs-strong{font-weight:700;color:#a07e3b}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#489963}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#1c9aa0}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#478c90}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#55859b}.hljs-emphasis{color:#55859b;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#867469}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}