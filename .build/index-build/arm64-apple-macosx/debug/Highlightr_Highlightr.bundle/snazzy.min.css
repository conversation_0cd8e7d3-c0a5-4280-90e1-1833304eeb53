/*!
  Theme: Snazzy
  Author: <PERSON><PERSON><PERSON> <PERSON> (https://github.com/chawyehsu) based on Hyper Snazzy Theme (https://github.com/sindresorhus/hyper-snazzy)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#e2e4e5;background:#282a36}.hljs ::selection,.hljs::selection{background-color:#43454f;color:#e2e4e5}.hljs-comment{color:#78787e}.hljs-tag{color:#a5a5a9}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#e2e4e5}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ff5c57}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ff9f43}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#f3f99d}.hljs-strong{font-weight:700;color:#f3f99d}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#5af78e}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#9aedfe}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#57c7ff}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ff6ac1}.hljs-emphasis{color:#ff6ac1;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b2643c}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}