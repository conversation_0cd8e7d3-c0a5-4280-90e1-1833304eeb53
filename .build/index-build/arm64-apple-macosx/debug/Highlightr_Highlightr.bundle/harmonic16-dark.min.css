/*!
  Theme: Harmonic16 Dark
  Author: <PERSON><PERSON> (https://github.com/janniks)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#cbd6e2;background:#0b1c2c}.hljs ::selection,.hljs::selection{background-color:#405c79;color:#cbd6e2}.hljs-comment{color:#627e99}.hljs-tag{color:#aabcce}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#cbd6e2}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#bf8b56}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#bfbf56}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#8bbf56}.hljs-strong{font-weight:700;color:#8bbf56}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#56bf8b}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#568bbf}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#8b56bf}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#bf568b}.hljs-emphasis{color:#bf568b;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#bf5656}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}