/*!
  Theme: Sandcastle
  Author: <PERSON> (https://github.com/gessig)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#a89984;background:#282c34}.hljs ::selection,.hljs::selection{background-color:#3e4451;color:#a89984}.hljs-comment{color:#665c54}.hljs-tag{color:#928374}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#a89984}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#83a598}.hljs-attr,.hljs-class .hljs-title,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-title,.hljs-title.class_,.hljs-variable.constant_{color:#a07e3b}.hljs-strong{font-weight:700;color:#a07e3b}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#528b8b}.hljs-attribute,.hljs-built_in,.hljs-doctag,.hljs-function .hljs-title,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#83a598}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#d75f5f}.hljs-emphasis{color:#d75f5f;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#a87322}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}