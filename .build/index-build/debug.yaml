client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "CAtomic-arm64-apple-macosx15.0-debug.module": ["<CAtomic-arm64-apple-macosx15.0-debug.module>"]
  "Highlightr-arm64-apple-macosx15.0-debug.module": ["<Highlightr-arm64-apple-macosx15.0-debug.module>"]
  "KeyboardShortcuts-arm64-apple-macosx15.0-debug.module": ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>"]
  "Markdown-arm64-apple-macosx15.0-debug.module": ["<Markdown-arm64-apple-macosx15.0-debug.module>"]
  "Nexus-arm64-apple-macosx15.0-debug.module": ["<Nexus-arm64-apple-macosx15.0-debug.module>"]
  "NexusPackageTests-arm64-apple-macosx15.0-debug.module": ["<NexusPackageTests-arm64-apple-macosx15.0-debug.module>"]
  "NexusTests-arm64-apple-macosx15.0-debug.module": ["<NexusTests-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "SQLite-arm64-apple-macosx15.0-debug.module": ["<SQLite-arm64-apple-macosx15.0-debug.module>"]
  "SwiftToolchainCSQLite-arm64-apple-macosx15.0-debug.module": ["<SwiftToolchainCSQLite-arm64-apple-macosx15.0-debug.module>"]
  "api_test-arm64-apple-macosx15.0-debug.module": ["<api_test-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-bin-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module>"]
  "main": ["<Nexus-arm64-apple-macosx15.0-debug.module>"]
  "sqlite-arm64-apple-macosx15.0-debug.module": ["<sqlite-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<Nexus-arm64-apple-macosx15.0-debug.module>","<NexusTests-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Volumes/ssd/projects/new nexus/Nexus/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Volumes/ssd/projects/new nexus/Tests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/1c-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/1c-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/3024.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/3024.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/a11y-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/agate.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/agate.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/an-old-hope.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/an-old-hope.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/androidstudio.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/androidstudio.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apathy.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apathy.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apprentice.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/apprentice.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arduino-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arduino-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arta.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/arta.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ascetic.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ascetic.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ashes.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ashes.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-cave.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-dune.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-estuary.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-forest.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-heath.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-lakeside.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-plateau.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-savanna.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-seaside.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atlas.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atlas.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark-reasonable.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark-reasonable.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/atom-one-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bespin.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bespin.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-bathory.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-bathory.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-burzum.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-burzum.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-dark-funeral.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-dark-funeral.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-gorgoroth.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-gorgoroth.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-immortal.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-immortal.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-khold.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-khold.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-marduk.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-marduk.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-mayhem.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-mayhem.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-nile.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-nile.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-venom.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal-venom.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/black-metal.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brewer.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brewer.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bright.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/bright.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brogrammer.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brogrammer.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brown-paper.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brown-paper.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/brush-trees.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/chalk.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/chalk.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/circus.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/circus.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/classic-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codepen-embed.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codepen-embed.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codeschool.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/codeschool.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/color-brewer.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/color-brewer.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/colors.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/colors.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupcake.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupcake.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupertino.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cupertino.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-cherry.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-cherry.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-dimmer.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-dimmer.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-icecap.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-icecap.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-saturated.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/cybertopia-saturated.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/danqing.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/danqing.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darcula.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darcula.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark-violet.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark-violet.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darkmoss.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darkmoss.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darktooth.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/darktooth.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/decaf.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/decaf.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/default.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/devibeans.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/devibeans.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dirtysea.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dirtysea.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/docco.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/docco.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dracula.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/dracula.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/edge-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eighties.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eighties.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/embers.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/embers.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/equilibrium-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/espresso.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/espresso.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva-dim.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva-dim.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/eva.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/far.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/far.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/felipec.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/felipec.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/flat.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/flat.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/foundation.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/foundation.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/framer.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/framer.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/fruit-soda.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/fruit-soda.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gigavolt.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gigavolt.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark-dimmed.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark-dimmed.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-gist.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github-gist.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/github.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gml.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gml.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/google-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/googlecode.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/googlecode.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gradient-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/grayscale.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/green-screen.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/green-screen.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-hard.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-hard.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-medium.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-medium.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-pale.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-pale.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-soft.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-soft.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-hard.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-hard.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-medium.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-medium.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-soft.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light-soft.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/gruvbox-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hardcore.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hardcore.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/harmonic16-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/heetch-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/helios.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/helios.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/highlighter/highlight.min.js"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/highlighter/highlight.min.js"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hopscotch.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hopscotch.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/horizon-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/humanoid-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hybrid.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/hybrid.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ia-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/icy-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/icy-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/idea.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/idea.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/intellij-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/intellij-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ir-black.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ir-black.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isbl-editor-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isotope.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/isotope.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimber.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimber.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/kimbie.light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lightfair.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lightfair.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lioshi.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/lioshi.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/london-tube.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/london-tube.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/macintosh.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/macintosh.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/magula.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/magula.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/marrakesh.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/marrakesh.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/materia.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/materia.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-darker.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-darker.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-lighter.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-lighter.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-palenight.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-palenight.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-vivid.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material-vivid.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/material.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mellow-purple.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mellow-purple.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mexico-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mexico-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mocha.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mocha.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mono-blue.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/mono-blue.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai-sublime.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai-sublime.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/monokai.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nebula.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nebula.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/night-owl.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/night-owl.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nnfx-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nord.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nord.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nova.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/nova.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/obsidian.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/obsidian.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ocean.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ocean.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/oceanicnext.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/oceanicnext.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/one-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/one-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/onedark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/onedark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/outrun-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/outrun-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/panda-syntax-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/papercolor-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/paraiso.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pasque.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pasque.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/phd.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/phd.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pico.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pico.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pojoaque.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pojoaque.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pop.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/pop.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/porple.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/porple.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/purebasic.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/purebasic.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qtcreator_light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qualia.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/qualia.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/railscasts.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/railscasts.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rainbow.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rainbow.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rebecca.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rebecca.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-dawn.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-dawn.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-moon.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine-moon.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/ros-pine.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-dawn.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-dawn.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-moon.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine-moon.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/rose-pine.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/routeros.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/routeros.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sagelight.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sagelight.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sandcastle.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sandcastle.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/school-book.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/school-book.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/seti-ui.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/seti-ui.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shades-of-purple.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shades-of-purple.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shapeshifter.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/shapeshifter.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/silk-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/snazzy.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/snazzy.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solar-flare.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/solarized-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/spacemacs.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/spacemacs.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/srcery.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/srcery.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/stackoverflow-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summercamp.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summercamp.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/summerfruit-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sunburst.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/sunburst.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tango.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tango.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tender.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tender.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tokyo-night-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-blue.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-blue.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-bright.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-bright.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-eighties.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night-eighties.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow-night.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/tomorrow.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/twilight.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/twilight.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/unikitty-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs2015.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vs2015.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vulcan.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/vulcan.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-10.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-95.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-high-contrast.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt-light.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt-light.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/windows-nt.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/woodland.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/woodland.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dark.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dusk.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode-dusk.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xcode.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xt256.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/xt256.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/zenburn.min.css"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/assets/styles/zenburn.min.css"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Info.plist"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Info.plist"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ar.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ar.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/cs.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/cs.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/de.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/de.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/en.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/en.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/es.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/es.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/fr.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/fr.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/hu.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/hu.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ja.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ja.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ko.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ko.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/nl.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/nl.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/pt-BR.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/pt-BR.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ru.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ru.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/sk.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/sk.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-Hans.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-Hans.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-TW.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-TW.lproj/Localizable.strings"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift","/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift","/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift","/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift","/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift","/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift","/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources"

  "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<Highlightr-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"]
    outputs: ["<Highlightr-arm64-apple-macosx15.0-debug.module>"]

  "<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"]
    outputs: ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>"]

  "<Markdown-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"]
    outputs: ["<Markdown-arm64-apple-macosx15.0-debug.module>"]

  "<Nexus-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"]
    outputs: ["<Nexus-arm64-apple-macosx15.0-debug.module>"]

  "<NexusPackageTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusPackageTests.swiftmodule"]
    outputs: ["<NexusPackageTests-arm64-apple-macosx15.0-debug.module>"]

  "<NexusTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule"]
    outputs: ["<NexusTests-arm64-apple-macosx15.0-debug.module>"]

  "<SQLite-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule"]
    outputs: ["<SQLite-arm64-apple-macosx15.0-debug.module>"]

  "C.Highlightr-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/CodeAttributedString.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/HTMLUtils.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Highlightr.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Shims.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/Highlightr/src/classes/Theme.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<Highlightr-arm64-apple-macosx15.0-debug.module-resources>","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"]
    description: "Compiling Swift Module 'Highlightr' (6 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Highlightr","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr.build/Highlightr-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.KeyboardShortcuts-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources>","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"]
    description: "Compiling Swift Module 'KeyboardShortcuts' (11 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","KeyboardShortcuts","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings","-package-name","keyboardshortcuts"]

  "C.Markdown-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/CAtomic.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/buffer.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/chunk.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm-extension_api.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark-gfm_version.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/cmark_ctype.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/export.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/footnotes.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/houdini.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/html.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/inlines.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/iterator.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/map.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/mutex.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/node.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/parser.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/plugin.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/references.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/registry.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/render.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/scanners.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/syntax_extension.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/utf8.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/autolink.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/ext_scanners.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/cmark-gfm-core-extensions.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/strikethrough.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/table.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/tagfilter.h","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/tasklist.h","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"]
    description: "Compiling Swift Module 'Markdown' (69 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Markdown","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markdown-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Nexus-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/Nexus/Accessibility/AccessibilityManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Accessibility/KeyboardNavigationManager.swift","/Volumes/ssd/projects/new nexus/Nexus/ContentView.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/ConfigurationManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/EnvironmentKeys.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/ModuleManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/NexusModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Core/SearchViewModel.swift","/Volumes/ssd/projects/new nexus/Nexus/Data/DataService.swift","/Volumes/ssd/projects/new nexus/Nexus/Data/DatabaseManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Editor/EditorView.swift","/Volumes/ssd/projects/new nexus/Nexus/Editor/NexusTextView.swift","/Volumes/ssd/projects/new nexus/Nexus/Models/AppConfiguration.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/ClipboardManagerModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/NotesHubModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/QuickLauncherModule.swift","/Volumes/ssd/projects/new nexus/Nexus/Modules/SmartCalculatorModule.swift","/Volumes/ssd/projects/new nexus/Nexus/NexusApp.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/EncryptionManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/KeychainManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Security/SecurityManager.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/NotesWindow.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsView.swift","/Volumes/ssd/projects/new nexus/Nexus/Views/SettingsWindowController.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"]
    description: "Compiling Swift Module 'Nexus' (24 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Nexus","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/output-file-map.json","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Nexus.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","Nexus_main","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","new_nexus"]

  "C.NexusPackageTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.derived/runner.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'NexusPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","NexusPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusPackageTests.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusPackageTests.build/NexusPackageTests-Swift.h","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","new_nexus"]

  "C.NexusTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/Tests/NexusTests/ConfigurationManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/DataServiceTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/DatabaseManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/EncryptionManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/ModuleManagerTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/SearchViewModelTests.swift","/Volumes/ssd/projects/new nexus/Tests/NexusTests/SecurityManagerTests.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule"]
    description: "Compiling Swift Module 'NexusTests' (7 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","NexusTests","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/NexusTests.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/NexusTests.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/extensions/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/CAtomic/include","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","new_nexus"]

  "C.SQLite-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift","/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources"]
    outputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule"]
    description: "Compiling Swift Module 'SQLite' (36 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","SQLite","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules/SQLite.swiftmodule","-output-file-map","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/output-file-map.json","-parse-as-library","-incremental","@/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/sources","-I","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLite-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings","-package-name","sqlite_swift"]

  "Highlightr-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css"]
    outputs: ["<Highlightr-arm64-apple-macosx15.0-debug.module-resources>"]

  "KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings","/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist"]
    outputs: ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Volumes/ssd/projects/new nexus/Nexus/","/Volumes/ssd/projects/new nexus/Tests/","/Volumes/ssd/projects/new nexus/Package.swift","/Volumes/ssd/projects/new nexus/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

