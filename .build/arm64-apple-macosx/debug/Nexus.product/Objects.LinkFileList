'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/AccessibilityManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/AppConfiguration.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/ClipboardManagerModule.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/ConfigurationManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/DataService.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/DatabaseManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/EditorView.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/EncryptionManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/EnvironmentKeys.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/KeyboardNavigationManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/KeychainManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/ModuleManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/NexusApp.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/NexusModule.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/NexusTextView.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesHubModule.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesWindow.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/QuickLauncherModule.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/SearchViewModel.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/SecurityManager.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/SettingsView.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/SettingsWindowController.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/Nexus.build/SmartCalculatorModule.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Backup.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Blob.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Cipher.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Coding.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Collation.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Connection.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Errors.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Expression.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/FTS4.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/FTS5.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Foundation.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Helpers.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Operators.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Query+with.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Query.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/RTree.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Result.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Schema.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/SchemaReader.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Setter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Statement.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/Value.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions.swift.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o'
'/Volumes/ssd/projects/new nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o'
