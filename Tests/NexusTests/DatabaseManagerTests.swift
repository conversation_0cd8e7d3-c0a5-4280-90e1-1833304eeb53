import XCTest
import Foundation
import SQLite
@testable import Nexus

/// Unit tests for DatabaseManager
final class DatabaseManagerTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var tempDirectory: URL!
    var securityManager: SecurityManager!
    
    override func setUp() {
        super.setUp()
        
        // Create temporary directory for test database
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("NexusTests")
            .appendingPathComponent(UUID().uuidString)
        
        try! FileManager.default.createDirectory(at: tempDirectory, 
                                               withIntermediateDirectories: true)
        
        // Initialize database manager
        databaseManager = DatabaseManager()
        
        // Initialize security manager for encryption tests
        securityManager = SecurityManager()
        let securitySettings = SecuritySettings(
            enableDataEncryption: true,
            enableNotesEncryption: true,
            enableClipboardEncryption: true,
            enableSecureErase: true,
            dataRetentionDays: 30,
            enableAuditLogging: true,
            enablePrivacyMode: true
        )
        securityManager.configure(with: securitySettings)
    }
    
    override func tearDown() {
        // Clean up temporary directory
        try? FileManager.default.removeItem(at: tempDirectory)
        databaseManager = nil
        securityManager = nil
        super.tearDown()
    }
    
    // MARK: - Note CRUD Tests
    
    func testInsertNote() throws {
        let note = Note(content: "Test note content")
        
        try databaseManager.insertNote(note)
        
        let retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 1)
        XCTAssertEqual(retrievedNotes.first?.content, "Test note content")
        XCTAssertEqual(retrievedNotes.first?.id, note.id)
    }
    
    func testInsertMultipleNotes() throws {
        let note1 = Note(content: "First note")
        let note2 = Note(content: "Second note")
        let note3 = Note(content: "Third note")
        
        try databaseManager.insertNote(note1)
        try databaseManager.insertNote(note2)
        try databaseManager.insertNote(note3)
        
        let retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 3)
        
        let contents = retrievedNotes.map { $0.content }
        XCTAssertTrue(contents.contains("First note"))
        XCTAssertTrue(contents.contains("Second note"))
        XCTAssertTrue(contents.contains("Third note"))
    }
    
    func testUpdateNote() throws {
        let originalNote = Note(content: "Original content")
        try databaseManager.insertNote(originalNote)
        
        let updatedNote = Note(
            id: originalNote.id,
            content: "Updated content",
            title: "Updated Title",
            createdAt: originalNote.createdAt,
            modifiedAt: Date(),
            isFavorite: true,
            isArchived: false
        )
        
        try databaseManager.updateNote(updatedNote)
        
        let retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 1)
        
        let retrievedNote = retrievedNotes.first!
        XCTAssertEqual(retrievedNote.content, "Updated content")
        XCTAssertEqual(retrievedNote.title, "Updated Title")
        XCTAssertTrue(retrievedNote.isFavorite)
        XCTAssertFalse(retrievedNote.isArchived)
    }
    
    func testDeleteNote() throws {
        let note = Note(content: "Note to delete")
        try databaseManager.insertNote(note)
        
        // Verify note exists
        var retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 1)
        
        // Delete note
        try databaseManager.deleteNote(note.id)
        
        // Verify note is deleted
        retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 0)
    }
    
    // TODO: Implement getNote(by:) method in DatabaseManager
    
    // MARK: - Folder CRUD Tests
    
    func testInsertFolder() throws {
        let folder = Folder(name: "Test Folder", color: "#FF0000")
        
        try databaseManager.insertFolder(folder)
        
        let retrievedFolders = try databaseManager.getAllFolders()
        XCTAssertEqual(retrievedFolders.count, 1)
        XCTAssertEqual(retrievedFolders.first?.name, "Test Folder")
        XCTAssertEqual(retrievedFolders.first?.color, "#FF0000")
    }
    
    func testInsertFolderWithParent() throws {
        let parentFolder = Folder(name: "Parent Folder", color: "#0000FF")
        try databaseManager.insertFolder(parentFolder)
        
        let childFolder = Folder(
            name: "Child Folder",
            color: "#00FF00",
            parentId: parentFolder.id
        )
        try databaseManager.insertFolder(childFolder)
        
        let retrievedFolders = try databaseManager.getAllFolders()
        XCTAssertEqual(retrievedFolders.count, 2)
        
        let child = retrievedFolders.first { $0.name == "Child Folder" }
        XCTAssertNotNil(child)
        XCTAssertEqual(child?.parentId, parentFolder.id)
    }
    
    // TODO: Implement updateFolder and deleteFolder methods in DatabaseManager
    
    // MARK: - Note-Folder Relationship Tests
    // TODO: Implement note-folder relationship methods in DatabaseManager
    
    // MARK: - Search Tests
    
    func testSearchNotes() throws {
        let note1 = Note(content: "This is about Swift programming")
        let note2 = Note(content: "Python is also a great language")
        let note3 = Note(content: "JavaScript for web development")
        
        try databaseManager.insertNote(note1)
        try databaseManager.insertNote(note2)
        try databaseManager.insertNote(note3)
        
        let swiftResults = try databaseManager.searchNotes(query: "Swift")
        XCTAssertEqual(swiftResults.count, 1)
        XCTAssertEqual(swiftResults.first?.content, "This is about Swift programming")

        let programmingResults = try databaseManager.searchNotes(query: "programming")
        XCTAssertEqual(programmingResults.count, 1)

        let languageResults = try databaseManager.searchNotes(query: "language")
        XCTAssertEqual(languageResults.count, 1)
        XCTAssertEqual(languageResults.first?.content, "Python is also a great language")
    }
    
    func testSearchNotesNoResults() throws {
        let note = Note(content: "This is a test note")
        try databaseManager.insertNote(note)
        
        let results = try databaseManager.searchNotes(query: "nonexistent")
        XCTAssertEqual(results.count, 0)
    }
    
    // MARK: - Encryption Integration Tests
    
    func testInsertNoteWithEncryption() async throws {
        // Configure database with security manager
        databaseManager.configure(securityManager: securityManager)
        
        // Wait for security manager initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        let note = Note(content: "Encrypted note content")
        
        try databaseManager.insertNote(note)
        
        let retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 1)
        XCTAssertEqual(retrievedNotes.first?.content, "Encrypted note content")
    }
    
    // MARK: - Error Handling Tests
    
    func testInsertDuplicateNote() throws {
        let note = Note(content: "Test note")
        
        try databaseManager.insertNote(note)
        
        // Inserting the same note again should not throw an error
        // but should update the existing note
        XCTAssertNoThrow(try databaseManager.insertNote(note))
        
        let retrievedNotes = try databaseManager.getAllNotes()
        XCTAssertEqual(retrievedNotes.count, 1)
    }
}
