import XCTest
import Foundation
import Crypto<PERSON>it
@testable import Nexus

/// Unit tests for SecurityManager
final class SecurityManagerTests: XCTestCase {
    var securityManager: SecurityManager!
    var testSettings: SecuritySettings!
    
    override func setUp() {
        super.setUp()
        securityManager = SecurityManager()
        
        // Create test security settings with encryption enabled
        testSettings = SecuritySettings(
            enableDataEncryption: true,
            enableClipboardEncryption: true,
            enableNotesEncryption: true,
            dataRetentionDays: 30,
            autoLockAfterMinutes: 15,
            requireAuthenticationOnLaunch: true,
            enableSecureErase: true,
            allowDataExport: false,
            enableAuditLogging: true
        )
    }
    
    override func tearDown() {
        // Clean up any test keys
        try? securityManager.resetSecurity()
        securityManager = nil
        testSettings = nil
        super.tearDown()
    }
    
    // MARK: - Configuration Tests
    
    func testInitialization() {
        XCTAssertFalse(securityManager.isEncryptionEnabled)
        XCTAssertFalse(securityManager.isClipboardEncryptionEnabled)
        XCTAssertFalse(securityManager.isNotesEncryptionEnabled)
    }
    
    func testConfigureWithSettings() async {
        securityManager.configure(with: testSettings)
        
        // Wait for async initialization
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        XCTAssertTrue(securityManager.isEncryptionEnabled)
        XCTAssertTrue(securityManager.isClipboardEncryptionEnabled)
        XCTAssertTrue(securityManager.isNotesEncryptionEnabled)
    }
    
    func testConfigureWithDisabledEncryption() {
        let disabledSettings = SecuritySettings(
            enableDataEncryption: false,
            enableClipboardEncryption: false,
            enableNotesEncryption: false,
            dataRetentionDays: 0,
            autoLockAfterMinutes: 0,
            requireAuthenticationOnLaunch: false,
            enableSecureErase: false,
            allowDataExport: true,
            enableAuditLogging: false
        )
        
        securityManager.configure(with: disabledSettings)
        
        XCTAssertFalse(securityManager.isEncryptionEnabled)
        XCTAssertFalse(securityManager.isClipboardEncryptionEnabled)
        XCTAssertFalse(securityManager.isNotesEncryptionEnabled)
    }
    
    // MARK: - Data Encryption Tests
    
    func testEncryptDecryptDataWhenEnabled() async throws {
        securityManager.configure(with: testSettings)
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        let originalData = "Test data for encryption".data(using: .utf8)!
        
        let encryptedData = try securityManager.encryptData(originalData)
        let decryptedData = try securityManager.decryptData(encryptedData)
        
        XCTAssertNotEqual(originalData, encryptedData)
        XCTAssertEqual(originalData, decryptedData)
    }
    
    func testEncryptDecryptDataWhenDisabled() throws {
        let disabledSettings = SecuritySettings.default // Encryption disabled by default
        securityManager.configure(with: disabledSettings)
        
        let originalData = "Test data".data(using: .utf8)!
        
        let encryptedData = try securityManager.encryptData(originalData)
        let decryptedData = try securityManager.decryptData(encryptedData)
        
        // When encryption is disabled, data should pass through unchanged
        XCTAssertEqual(originalData, encryptedData)
        XCTAssertEqual(originalData, decryptedData)
    }
    
    func testEncryptDecryptString() async throws {
        securityManager.configure(with: testSettings)
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        let originalString = "Test string with special characters: 🔐🚀"
        
        let encryptedData = try securityManager.encryptString(originalString)
        let decryptedString = try securityManager.decryptString(encryptedData)
        
        XCTAssertEqual(originalString, decryptedString)
    }
    
    // MARK: - Notes Encryption Tests
    
    func testEncryptDecryptNoteContentWhenEnabled() async throws {
        securityManager.configure(with: testSettings)
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        let noteContent = "This is a private note with sensitive information."
        
        let encryptedData = try securityManager.encryptNoteContent(noteContent)
        let decryptedContent = try securityManager.decryptNoteContent(encryptedData)
        
        XCTAssertEqual(noteContent, decryptedContent)
    }
    
    func testEncryptDecryptNoteContentWhenDisabled() throws {
        let disabledSettings = SecuritySettings(
            enableDataEncryption: false,
            enableClipboardEncryption: false,
            enableNotesEncryption: false,
            dataRetentionDays: 0,
            autoLockAfterMinutes: 0,
            requireAuthenticationOnLaunch: false,
            enableSecureErase: false,
            allowDataExport: true,
            enableAuditLogging: false
        )
        securityManager.configure(with: disabledSettings)
        
        let noteContent = "This is a note."
        
        let encryptedData = try securityManager.encryptNoteContent(noteContent)
        let decryptedContent = try securityManager.decryptNoteContent(encryptedData)
        
        // When notes encryption is disabled, content should pass through unchanged
        XCTAssertEqual(noteContent, decryptedContent)
    }
    
    // MARK: - Clipboard Encryption Tests
    
    func testEncryptDecryptClipboardContentWhenEnabled() async throws {
        securityManager.configure(with: testSettings)
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        let clipboardContent = "Sensitive clipboard data"
        
        let encryptedData = try securityManager.encryptClipboardContent(clipboardContent)
        let decryptedContent = try securityManager.decryptClipboardContent(encryptedData)
        
        XCTAssertEqual(clipboardContent, decryptedContent)
    }
    
    func testEncryptDecryptClipboardContentWhenDisabled() throws {
        let disabledSettings = SecuritySettings(
            enableDataEncryption: false,
            enableClipboardEncryption: false,
            enableNotesEncryption: false,
            dataRetentionDays: 0,
            autoLockAfterMinutes: 0,
            requireAuthenticationOnLaunch: false,
            enableSecureErase: false,
            allowDataExport: true,
            enableAuditLogging: false
        )
        securityManager.configure(with: disabledSettings)
        
        let clipboardContent = "Clipboard data"
        
        let encryptedData = try securityManager.encryptClipboardContent(clipboardContent)
        let decryptedContent = try securityManager.decryptClipboardContent(encryptedData)
        
        // When clipboard encryption is disabled, content should pass through unchanged
        XCTAssertEqual(clipboardContent, decryptedContent)
    }
    
    // MARK: - Secure Erase Tests
    
    func testSecureEraseWhenEnabled() {
        securityManager.configure(with: testSettings)
        
        var testData = "Sensitive data to be erased".data(using: .utf8)!
        let originalData = testData
        
        securityManager.secureErase(&testData)
        
        // Data should be overwritten
        XCTAssertNotEqual(testData, originalData)
        XCTAssertEqual(testData, Data(repeating: 0, count: originalData.count))
    }
    
    func testSecureEraseWhenDisabled() {
        let disabledSettings = SecuritySettings.default
        securityManager.configure(with: disabledSettings)
        
        var testData = "Data to be erased".data(using: .utf8)!
        let originalData = testData
        
        securityManager.secureErase(&testData)
        
        // When secure erase is disabled, data should remain unchanged
        XCTAssertEqual(testData, originalData)
    }
    
    // MARK: - Data Retention Tests
    
    func testShouldRetainDataWithinRetentionPeriod() {
        securityManager.configure(with: testSettings) // 30 days retention
        
        let recentDate = Date().addingTimeInterval(-24 * 60 * 60) // 1 day ago
        
        XCTAssertTrue(securityManager.shouldRetainData(createdAt: recentDate))
    }
    
    func testShouldRetainDataBeyondRetentionPeriod() {
        securityManager.configure(with: testSettings) // 30 days retention
        
        let oldDate = Date().addingTimeInterval(-35 * 24 * 60 * 60) // 35 days ago
        
        XCTAssertFalse(securityManager.shouldRetainData(createdAt: oldDate))
    }
    
    func testShouldRetainDataWithZeroRetentionDays() {
        let noRetentionSettings = SecuritySettings(
            enableDataEncryption: true,
            enableClipboardEncryption: true,
            enableNotesEncryption: true,
            dataRetentionDays: 0, // No retention limit
            autoLockAfterMinutes: 15,
            requireAuthenticationOnLaunch: true,
            enableSecureErase: true,
            allowDataExport: false,
            enableAuditLogging: true
        )
        securityManager.configure(with: noRetentionSettings)
        
        let veryOldDate = Date().addingTimeInterval(-365 * 24 * 60 * 60) // 1 year ago
        
        XCTAssertTrue(securityManager.shouldRetainData(createdAt: veryOldDate))
    }
    
    // MARK: - Reset Security Tests
    
    func testResetSecurity() async throws {
        securityManager.configure(with: testSettings)
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000)
        
        // Verify encryption works
        let testData = "Test data".data(using: .utf8)!
        XCTAssertNoThrow(try securityManager.encryptData(testData))
        
        // Reset security
        try securityManager.resetSecurity()
        
        // After reset, encryption should be disabled or fail
        // (depending on implementation details)
    }
    
    // MARK: - Cleanup Expired Data Tests
    
    func testCleanupExpiredData() async {
        securityManager.configure(with: testSettings)
        
        // This is mainly a smoke test since the actual cleanup
        // is handled by individual modules
        await securityManager.cleanupExpiredData()
        
        // Should complete without throwing
    }
}
