import XCTest
import Foundation
@testable import Nexus

/// Unit tests for ConfigurationManager
final class ConfigurationManagerTests: XCTestCase {
    var configurationManager: ConfigurationManager!
    var tempDirectory: URL!
    
    override func setUp() {
        super.setUp()
        
        // Create temporary directory for test configuration
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("NexusTests")
            .appendingPathComponent(UUID().uuidString)
        
        try! FileManager.default.createDirectory(at: tempDirectory, 
                                               withIntermediateDirectories: true)
        
        // Initialize configuration manager with test directory
        configurationManager = ConfigurationManager()
    }
    
    override func tearDown() {
        // Clean up temporary directory
        try? FileManager.default.removeItem(at: tempDirectory)
        configurationManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization() {
        XCTAssertNotNil(configurationManager.configuration)
        XCTAssertEqual(configurationManager.configuration.editorSettings.markdownRenderingEnabled, false)
        XCTAssertEqual(configurationManager.configuration.editorSettings.fontSize, 14)
    }

    func testDefaultConfiguration() {
        let defaultConfig = AppConfiguration.default

        // Test editor settings defaults
        XCTAssertFalse(defaultConfig.editorSettings.markdownRenderingEnabled)
        XCTAssertEqual(defaultConfig.editorSettings.fontSize, 14)
        XCTAssertEqual(defaultConfig.editorSettings.fontFamily, "SF Mono")
        
        // Test module settings defaults
        XCTAssertTrue(defaultConfig.calculatorSettings.isEnabled)
        XCTAssertTrue(defaultConfig.clipboardSettings.isEnabled)
        XCTAssertTrue(defaultConfig.notesHubSettings.isEnabled)
        XCTAssertTrue(defaultConfig.quickLauncherSettings.isEnabled)
        
        // Test security settings defaults
        XCTAssertFalse(defaultConfig.securitySettings.enableDataEncryption)
        XCTAssertFalse(defaultConfig.securitySettings.enableNotesEncryption)
        XCTAssertFalse(defaultConfig.securitySettings.enableClipboardEncryption)
    }
    
    // MARK: - Configuration Persistence Tests
    
    func testSaveConfiguration() {
        // Modify configuration
        let newEditorSettings = EditorSettings(
            fontSize: 16,
            fontFamily: "Menlo",
            lineSpacing: 1.0,
            showLineNumbers: true,
            wrapText: false,
            markdownRenderingEnabled: false,
            syntaxHighlightingEnabled: false
        )

        configurationManager.updateEditorSettings(newEditorSettings)

        // Verify changes were applied
        XCTAssertFalse(configurationManager.configuration.editorSettings.markdownRenderingEnabled)
        XCTAssertEqual(configurationManager.configuration.editorSettings.fontSize, 16)
        XCTAssertEqual(configurationManager.configuration.editorSettings.fontFamily, "Menlo")
    }
    
    func testUpdateModuleSettings() {
        // Test calculator settings update
        let newCalculatorSettings = CalculatorModuleSettings(
            isEnabled: false,
            decimalPlaces: 4,
            angleUnit: .radians,
            enableUnitConversions: false,
            enableCurrencyConversions: false,
            keyboardShortcut: nil
        )
        
        configurationManager.updateCalculatorSettings(newCalculatorSettings)
        
        XCTAssertFalse(configurationManager.configuration.calculatorSettings.isEnabled)
        XCTAssertEqual(configurationManager.configuration.calculatorSettings.decimalPlaces, 4)
        XCTAssertEqual(configurationManager.configuration.calculatorSettings.angleUnit, .radians)
    }
    
    func testUpdateSecuritySettings() {
        let newSecuritySettings = SecuritySettings(
            enableDataEncryption: true,
            enableClipboardEncryption: true,
            enableNotesEncryption: true,
            dataRetentionDays: 30,
            autoLockAfterMinutes: 15,
            requireAuthenticationOnLaunch: true,
            enableSecureErase: true,
            allowDataExport: false,
            enableAuditLogging: true
        )

        configurationManager.updateSecuritySettings(newSecuritySettings)

        XCTAssertTrue(configurationManager.configuration.securitySettings.enableDataEncryption)
        XCTAssertTrue(configurationManager.configuration.securitySettings.enableNotesEncryption)
        XCTAssertTrue(configurationManager.configuration.securitySettings.enableClipboardEncryption)
        XCTAssertEqual(configurationManager.configuration.securitySettings.dataRetentionDays, 30)
    }
    
    // MARK: - Module Toggle Tests
    
    func testToggleModuleEnabled() {
        // Test calculator module toggle
        let initialState = configurationManager.configuration.calculatorSettings.isEnabled
        configurationManager.toggleModuleEnabled("smart-calculator")
        XCTAssertNotEqual(configurationManager.configuration.calculatorSettings.isEnabled, initialState)
        
        // Toggle back
        configurationManager.toggleModuleEnabled("smart-calculator")
        XCTAssertEqual(configurationManager.configuration.calculatorSettings.isEnabled, initialState)
    }
    
    func testToggleAllModules() {
        let moduleIds = ["smart-calculator", "clipboard-manager", "notes-hub", "quick-launcher"]
        
        for moduleId in moduleIds {
            let initialState = getModuleEnabledState(moduleId)
            configurationManager.toggleModuleEnabled(moduleId)
            let newState = getModuleEnabledState(moduleId)
            XCTAssertNotEqual(newState, initialState, "Module \(moduleId) should have toggled")
        }
    }
    
    // MARK: - Reset Tests
    
    func testResetToDefaults() {
        // Modify configuration
        configurationManager.updateEditorSettings(EditorSettings(
            fontSize: 20,
            fontFamily: "Custom Font",
            lineSpacing: 2.0,
            showLineNumbers: false,
            wrapText: true,
            markdownRenderingEnabled: false,
            syntaxHighlightingEnabled: false
        ))

        // Reset to defaults
        configurationManager.resetToDefaults()

        // Verify reset
        let defaultConfig = AppConfiguration.default
        XCTAssertEqual(configurationManager.configuration.editorSettings.markdownRenderingEnabled,
                      defaultConfig.editorSettings.markdownRenderingEnabled)
        XCTAssertEqual(configurationManager.configuration.editorSettings.fontSize,
                      defaultConfig.editorSettings.fontSize)
        XCTAssertEqual(configurationManager.configuration.editorSettings.fontFamily,
                      defaultConfig.editorSettings.fontFamily)
    }
    
    // MARK: - Helper Methods
    
    private func getModuleEnabledState(_ moduleId: String) -> Bool {
        switch moduleId {
        case "smart-calculator":
            return configurationManager.configuration.calculatorSettings.isEnabled
        case "clipboard-manager":
            return configurationManager.configuration.clipboardSettings.isEnabled
        case "notes-hub":
            return configurationManager.configuration.notesHubSettings.isEnabled
        case "quick-launcher":
            return configurationManager.configuration.quickLauncherSettings.isEnabled
        default:
            return false
        }
    }
}
